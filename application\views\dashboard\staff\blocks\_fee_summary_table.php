<div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;">
    <div class="card-header panel_heading_new_style_staff" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
        <div class="card-title card-title-new-style">
          Fee Summary
          <ul class="panel-controls panel-controls-title mt-0">                                        
              <li style="width: 15rem;position: relative;top: -2px;">
                  <select class="form-control select" multiple title="All Fee Types" id="fee_type" name="fee_type"
                          data-actions-box="true"
                          data-live-search="true"
                          data-selected-text-format="count > 2"
                          data-none-selected-text="All Fee Types"
                          data-live-search-placeholder="Search fee types...">
                      <?php foreach ($fee_blueprints as $key => $val) { ?>
                          <option value="<?= $val->id ?>"><?php echo $val->name?></option>
                      <?php } ?>
                  </select>
              </li>                                
          </ul> 
        </div>
    </div>
    <div class="card-body pt-0">
        <div class="col-md-5 p-0" id="feeWidget">
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#95b75d;"></i> FeesPaid</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#fe970a;"></i> Balance</span>&emsp;
            <br><span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#428bca;"></i> Concession</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#E04B4A;"></i> NonReconciled</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#212529;"></i> Refund</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#ffa3a3;"></i> Previous Balance</span>&emsp;<br>
            <span style="font-size: 14px;"><i class="fa fa-square" aria-hidden="true" style="color:#1ad58f;"></i> Excess Amount</span>&emsp;
        </div>
        <div class="col-md-7 p-0">
            <div class="loadingClassNew" id="fees_summary_widget"></div>
            <div id="myfirstchart1" style="height: 200px;cursor: pointer;">
            </div>
        </div>
    </div>
    <div class="card-body pt-0">
    <span style="font-size: 14px;"> Admission Status</span>&emsp;
        <div style="display: flex; justify-content: space-around;margin-top:0.5rem" id="legend" >
            <?php foreach ($admissionStatus as $key => $val) { ?>
                <div class="legend-list">
                    <input type="checkbox" style="width:14px;height:14px;" value="<?php echo $key ?>" class="legend-checkbox admission_status" name="student-status" <?php if ($key == 1 || $key == 2) echo 'checked'; ?>>
                    <span style="font-size: 14px;"><?php echo $val ?></span>&emsp;
                </div>
            <?php } ?>
        </div>
    </div>
</div>

<style type="text/css">
    #feeWidget span{
        line-height: 30px;
    }
    .loadingClassNew {
      border: 8px solid #eee;
      border-top: 8px solid #7193be;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      position: fixed;
      z-index: 1;
      animation: spin 2s linear infinite;
      margin-top: 35%;
      margin-left: 40%;
      position: absolute;
      z-index: 99999;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Bootstrap Select Multiselect Styling */
    .card .bootstrap-select {
        width: 100% !important;
    }

    .card .bootstrap-select .btn {
        border: 1px solid #ddd;
        background-color: #fff;
        color: #333;
        text-align: left;
        height: 38px;
        padding: 6px 12px;
        font-size: 14px;
    }

    .card .bootstrap-select .btn:focus,
    .card .bootstrap-select .btn:active {
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        border-color: #007bff;
    }

    /* Dropdown positioning within widget */
    .panel-controls-title .bootstrap-select .dropdown-menu {
        right: 0;
        left: auto;
        min-width: 280px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1050;
        border: 1px solid #ccc;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* Search box styling */
    .bootstrap-select .bs-searchbox input {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 4px 8px;
        margin: 4px 8px 8px 8px;
        width: calc(100% - 16px);
    }

    .bootstrap-select .bs-searchbox input:focus {
        border-color: #007bff;
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    /* Actions box styling */
    .bootstrap-select .bs-actionsbox {
        padding: 4px 8px;
        border-bottom: 1px solid #eee;
    }

    .bootstrap-select .bs-actionsbox .btn {
        margin-right: 5px;
        font-size: 12px;
        padding: 2px 8px;
        height: auto;
        border: 1px solid #ddd;
        background-color: #f8f9fa;
    }

    .bootstrap-select .bs-actionsbox .btn:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    /* Dropdown items styling */
    .bootstrap-select .dropdown-menu li a {
        padding: 6px 20px;
        font-size: 14px;
    }

    .bootstrap-select .dropdown-menu li a:hover {
        background-color: #f8f9fa;
    }

    .bootstrap-select .dropdown-menu li.selected a {
        background-color: #007bff;
        color: white;
    }

    /* Caret styling */
    .bootstrap-select .btn .caret {
        margin-left: 5px;
        vertical-align: middle;
        border-top: 4px solid #333;
        border-right: 4px solid transparent;
        border-left: 4px solid transparent;
    }

    /* Prevent dropdown from disappearing during search */
    .bootstrap-select.open .dropdown-menu {
        display: block !important;
    }

    .bootstrap-select .dropdown-menu.inner {
        position: static;
        border: 0;
        padding: 0;
        margin: 0;
        border-radius: 0;
        box-shadow: none;
    }

</style>
<script>

$(document).ready(function(){
    // Initialize Bootstrap selectpicker for multiselect dropdown
    if (typeof $.fn.selectpicker !== 'undefined') {
        $('#fee_type').selectpicker({
            noneSelectedText: 'All Fee Types',
            selectAllText: 'Select All',
            deselectAllText: 'Deselect All',
            countSelectedText: '{0} of {1} selected',
            actionsBox: true,
            liveSearch: true,
            liveSearchPlaceholder: 'Search fee types...',
            dropupAuto: false,
            container: 'body',
            style: 'btn-default',
            size: 'auto'
        });
    } else {
        console.warn('Bootstrap selectpicker not loaded');
    }

    updateFeeSummary();

    $('.legend-checkbox').on('change', function() {
        updateFeeSummary();
    });

    // Use Bootstrap selectpicker event for multiselect
    $('#fee_type').on('changed.bs.select', function() {
        updateFeeSummary();
    });

    // Fallback for regular change event
    $('#fee_type').on('change', function() {
        updateFeeSummary();
    });

    // Prevent dropdown from closing during search
    $(document).on('click', '.bootstrap-select .bs-searchbox input', function(e) {
        e.stopPropagation();
    });

    // Handle keyboard navigation
    $(document).on('keydown', '#fee_type', function(e) {
        if (e.which === 13 || e.which === 32) { // Enter or Space
            e.preventDefault();
            $(this).selectpicker('toggle');
        }
    });

    // Refresh selectpicker after a delay to ensure proper initialization
    setTimeout(function() {
        if (typeof $.fn.selectpicker !== 'undefined') {
            $('#fee_type').selectpicker('refresh');
        }
    }, 100);

});

function updateFeeSummary() {
    var fee_type = $('#fee_type').val() || 0;
    var student_status = [];
    $('input[name="student-status"]:checked').each(function() {
        student_status.push($(this).val());
    });
   
    get_fee_summary_amount(fee_type, student_status);
}

function get_fee_summary_amount(bpId, student_admission_status) {
    console.log(bpId);
    console.log(student_admission_status);
    var gData = [];
    $('#fees_summary_widget').show();
    if ($('#myfirstchart1').data('morris')) {
        $('#myfirstchart1').data('morris').destroy();
    }
    $('#myfirstchart1').empty();
    $.ajax({
        url: '<?php echo site_url('dashboard/get_fee_summary_details'); ?>',
        type: 'post',
        data: {'bpId': bpId, 'student_admission_status': student_admission_status},
        success: function(data) {
            var rData = JSON.parse(data);
             $('#fees_summary_widget').hide();
             if(!rData){
                return false;
             }

            gData.push(
              { label: 'Fee paid', value:  rData.fee_paid}, 
              { label: 'Balance', value:  rData.balance},
              { label: 'Concession', value:  rData.concession},
              { label: 'NonReconciled', value:  rData.recon_amount},
            );
            var refund_amount = 0;
            if (rData.refund_amount != 0) {
                refund_amount = rData.refund_amount;
            }
            gData.push({ label: 'Refund', value: refund_amount});
            var prevousYearname = '';
            var previousBalance = 0;
            if (rData.previousBalance != 0) {
                prevousYearname = rData.prevousYearname;
                previousBalance = rData.previousBalance;
            }
            gData.push({ label: 'Previous Balance:'+prevousYearname+'', value: previousBalance});
            var excess_amount = 0;
            if (rData.excess_amount != 0) {
                excess_amount = rData.excess_amount;
            }
            gData.push({ label: 'Excess Amount', value: excess_amount});
            $('#myfirstchart1').empty();
            renderingDonut(gData);
        }
    });
}
function renderingDonut(gData){
    new Morris.Donut({
        element: 'myfirstchart1',
        data: gData,
        colors:['#95b75d','#fe970a','#428bca','#E04B4A','#212529','#ffa3a3','#1ad58f'],
        
        formatter: function (y) { return new Intl.NumberFormat('en-IN',{ style: 'currency', currency: 'INR' }).format(y) }
    }).on('click',function(i, row){
            displayData(i, row);
    });
}


function displayData(i, row) {
    var fee_type = $('#fee_type').val();
    if (fee_type == null) {
        fee_type = 0;
    }
    switch(i){
        // Fee Collected
        case 0:
        var url = '<?php echo site_url('feesv2/reports_v2/student_wise_fees_details/') ?>'+fee_type;
        break;
        // Balance
        case 1:
        var url='<?php echo site_url('feesv2/reports_v2/balance_report/') ?>'+fee_type;
        break;
        // Concession
        case 2:
        var url='<?php echo site_url('feesv2/reports/concessions_new/') ?>'+fee_type;
        break;
        // Non-reconciled
        case 3:
        var url='<?php echo site_url('feesv2/reports/reconciled_report/') ?>';
        break;
        default:
        var url='';
        break;
    }
    if(url !=''){
        window.location.href = url;
    }  
}
      

</script>
